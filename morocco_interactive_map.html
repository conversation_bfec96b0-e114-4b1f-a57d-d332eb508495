<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carte Interactive du Maroc</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Leaflet Draw CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.css" />
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #c41e3a, #2e8b57);
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 300px;
            background: white;
            padding: 20px;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        .tool-section {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .tool-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            border-bottom: 2px solid #c41e3a;
            padding-bottom: 5px;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 15px;
            margin: 5px 5px 5px 0;
            background: #c41e3a;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #a01729;
        }
        
        .btn.active {
            background: #2e8b57;
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #545b62;
        }
        
        .info-box {
            background: #e8f4f8;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-size: 13px;
        }
        
        .distance-result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-weight: bold;
        }
        
        .coordinates {
            font-family: monospace;
            font-size: 12px;
            color: #666;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🇲🇦 Carte Interactive du Maroc</h1>
        <p>Outils de dessin, mesure et analyse géographique</p>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="tool-section">
                <h3>🎯 Outils de Dessin</h3>
                <button class="btn" onclick="toggleDrawing('circle')">🔵 Cercle</button>
                <button class="btn" onclick="toggleDrawing('rectangle')">⬜ Rectangle</button>
                <button class="btn" onclick="toggleDrawing('polygon')">🔷 Polygone</button>
                <button class="btn" onclick="toggleDrawing('marker')">📍 Marqueur</button>
                <div class="info-box">
                    Cliquez sur un outil puis dessinez sur la carte
                </div>
            </div>
            
            <div class="tool-section">
                <h3>📏 Mesure de Distance</h3>
                <button class="btn" onclick="toggleMeasure()" id="measureBtn">📐 Mesurer</button>
                <button class="btn secondary" onclick="clearMeasurements()">🗑️ Effacer</button>
                <div id="distanceResult"></div>
                <div class="info-box">
                    Cliquez sur deux points pour mesurer la distance
                </div>
            </div>
            
            <div class="tool-section">
                <h3>🗂️ Gestion</h3>
                <button class="btn secondary" onclick="clearAll()">🗑️ Tout Effacer</button>
                <button class="btn secondary" onclick="exportData()">💾 Exporter</button>
                <div class="info-box">
                    Gérez vos dessins et données
                </div>
            </div>
            
            <div class="tool-section">
                <h3>ℹ️ Informations</h3>
                <div id="mapInfo">
                    <p><strong>Zoom:</strong> <span id="zoomLevel">-</span></p>
                    <p><strong>Centre:</strong></p>
                    <div id="centerCoords" class="coordinates">-</div>
                    <p><strong>Objets:</strong> <span id="objectCount">0</span></p>
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map">
                <div class="loading">
                    🔄 Chargement de la carte...
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- Leaflet Draw JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.js"></script>
    
    <script>
        // Variables globales
        let map;
        let moroccoLayer;
        let drawnItems;
        let measureMode = false;
        let measurePoints = [];
        let measureLine = null;
        let currentTool = null;
        
        // Initialisation de la carte
        async function initMap() {
            try {
                // Créer la carte centrée sur le Maroc
                map = L.map('map').setView([31.7917, -7.0926], 6);
                
                // Ajouter les tuiles de base
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map);
                
                // Groupe pour les dessins
                drawnItems = new L.FeatureGroup();
                map.addLayer(drawnItems);
                
                // Charger la carte du Maroc
                await loadMoroccoMap();
                
                // Configurer les événements
                setupEventListeners();
                
                // Mettre à jour les infos
                updateMapInfo();
                
                console.log('✅ Carte initialisée avec succès');
                
            } catch (error) {
                console.error('❌ Erreur initialisation carte:', error);
                document.getElementById('map').innerHTML = 
                    '<div class="loading" style="color: red;">❌ Erreur de chargement de la carte</div>';
            }
        }
        
        // Charger la carte du Maroc
        async function loadMoroccoMap() {
            try {
                const response = await fetch('morocco_filtered.geojson');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const geoJsonData = await response.json();
                
                // Style de la carte du Maroc
                const moroccoStyle = {
                    fillColor: '#ffebcd',
                    weight: 2,
                    opacity: 1,
                    color: '#c41e3a',
                    dashArray: '',
                    fillOpacity: 0.3
                };
                
                // Ajouter la couche GeoJSON
                moroccoLayer = L.geoJSON(geoJsonData, {
                    style: moroccoStyle,
                    onEachFeature: function(feature, layer) {
                        layer.bindPopup('<strong>🇲🇦 Royaume du Maroc</strong><br>Cliquez pour plus d\'infos');
                    }
                }).addTo(map);
                
                // Ajuster la vue sur le Maroc
                map.fitBounds(moroccoLayer.getBounds(), {padding: [20, 20]});
                
                console.log('✅ Carte du Maroc chargée');
                
            } catch (error) {
                console.error('❌ Erreur chargement GeoJSON:', error);
                // Fallback: continuer sans la carte du Maroc
                console.log('⚠️ Continuation sans la carte GeoJSON');
            }
        }
        
        // Configuration des événements
        function setupEventListeners() {
            // Événements de la carte
            map.on('zoomend moveend', updateMapInfo);
            map.on('click', handleMapClick);
            
            // Événements des dessins
            map.on(L.Draw.Event.CREATED, function(e) {
                drawnItems.addLayer(e.layer);
                updateObjectCount();
            });
        }
        
        // Gestion des clics sur la carte
        function handleMapClick(e) {
            if (measureMode) {
                addMeasurePoint(e.latlng);
            }
        }
        
        // Basculer le mode de dessin
        function toggleDrawing(tool) {
            // Désactiver le mode mesure
            if (measureMode) {
                toggleMeasure();
            }
            
            // Réinitialiser les boutons
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            
            if (currentTool === tool) {
                currentTool = null;
                return;
            }
            
            currentTool = tool;
            event.target.classList.add('active');
            
            // Activer l'outil de dessin approprié
            switch(tool) {
                case 'circle':
                    new L.Draw.Circle(map, {}).enable();
                    break;
                case 'rectangle':
                    new L.Draw.Rectangle(map, {}).enable();
                    break;
                case 'polygon':
                    new L.Draw.Polygon(map, {}).enable();
                    break;
                case 'marker':
                    new L.Draw.Marker(map, {}).enable();
                    break;
            }
        }
        
        // Basculer le mode mesure
        function toggleMeasure() {
            measureMode = !measureMode;
            const btn = document.getElementById('measureBtn');
            
            if (measureMode) {
                btn.textContent = '🛑 Arrêter';
                btn.classList.add('active');
                clearMeasurements();
            } else {
                btn.textContent = '📐 Mesurer';
                btn.classList.remove('active');
            }
            
            // Désactiver les autres outils
            if (measureMode) {
                currentTool = null;
                document.querySelectorAll('.btn').forEach(btn => {
                    if (btn.id !== 'measureBtn') btn.classList.remove('active');
                });
            }
        }
        
        // Ajouter un point de mesure
        function addMeasurePoint(latlng) {
            measurePoints.push(latlng);
            
            // Ajouter un marqueur
            const marker = L.circleMarker(latlng, {
                radius: 5,
                fillColor: '#ff0000',
                color: '#ffffff',
                weight: 2,
                fillOpacity: 0.8
            }).addTo(map);
            
            if (measurePoints.length === 2) {
                // Calculer et afficher la distance
                const distance = measurePoints[0].distanceTo(measurePoints[1]);
                displayDistance(distance);
                
                // Dessiner la ligne
                measureLine = L.polyline(measurePoints, {
                    color: '#ff0000',
                    weight: 3,
                    dashArray: '5, 10'
                }).addTo(map);
                
                // Réinitialiser pour la prochaine mesure
                measurePoints = [];
            }
        }
        
        // Afficher la distance
        function displayDistance(distance) {
            const km = (distance / 1000).toFixed(2);
            const miles = (distance * 0.000621371).toFixed(2);
            
            document.getElementById('distanceResult').innerHTML = `
                <div class="distance-result">
                    📏 Distance: ${km} km<br>
                    📏 Distance: ${miles} miles
                </div>
            `;
        }
        
        // Effacer les mesures
        function clearMeasurements() {
            measurePoints = [];
            if (measureLine) {
                map.removeLayer(measureLine);
                measureLine = null;
            }
            
            // Supprimer les marqueurs de mesure
            map.eachLayer(function(layer) {
                if (layer instanceof L.CircleMarker && layer.options.fillColor === '#ff0000') {
                    map.removeLayer(layer);
                }
            });
            
            document.getElementById('distanceResult').innerHTML = '';
        }
        
        // Tout effacer
        function clearAll() {
            drawnItems.clearLayers();
            clearMeasurements();
            updateObjectCount();
        }
        
        // Exporter les données
        function exportData() {
            const data = {
                drawings: drawnItems.toGeoJSON(),
                center: map.getCenter(),
                zoom: map.getZoom(),
                timestamp: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `morocco_map_data_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // Mettre à jour les informations de la carte
        function updateMapInfo() {
            document.getElementById('zoomLevel').textContent = map.getZoom();
            const center = map.getCenter();
            document.getElementById('centerCoords').textContent = 
                `${center.lat.toFixed(4)}, ${center.lng.toFixed(4)}`;
        }
        
        // Mettre à jour le compteur d'objets
        function updateObjectCount() {
            const count = Object.keys(drawnItems._layers).length;
            document.getElementById('objectCount').textContent = count;
        }
        
        // Initialiser au chargement de la page
        document.addEventListener('DOMContentLoaded', initMap);
    </script>
</body>
</html>
