#!/usr/bin/env python3
"""
Script pour créer une carte du Maroc filtrée sans le Sahara occidental
"""

import json

def filter_morocco_map(input_file, output_file):
    """
    Filtre la carte du Maroc pour enlever le Sahara occidental
    et garder seulement le Maroc continental avec un peu de contexte
    """
    try:
        # Lire le fichier original
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📖 Lecture du fichier: {input_file}")
        
        # Paramètres de filtrage
        # Latitude minimum : 26.5° (enlève le Sahara occidental mais garde un peu du sud)
        # Longitude : -13.5° à -0.5° (océan à gauche, un peu d'Algérie à droite)
        MIN_LAT = 26.5  # Enlève le Sahara occidental
        MAX_LAT = 36.0  # Garde un peu au nord
        MIN_LON = -13.5 # Un peu d'océan à gauche
        MAX_LON = -0.5  # Un peu d'Algérie à droite
        
        print(f"🔧 Filtrage avec les limites:")
        print(f"   Latitude: {MIN_LAT}° à {MAX_LAT}°")
        print(f"   Longitude: {MIN_LON}° à {MAX_LON}°")
        
        filtered_geometries = []
        total_points_original = 0
        total_points_filtered = 0
        
        # Traiter chaque géométrie
        for geometry in data['geometries']:
            if geometry['type'] == 'Polygon':
                filtered_rings = []
                
                for ring in geometry['coordinates']:
                    filtered_ring = []
                    
                    for coord in ring:
                        lon, lat = coord[0], coord[1]
                        total_points_original += 1
                        
                        # Garder seulement les points dans la zone désirée
                        if (MIN_LAT <= lat <= MAX_LAT and 
                            MIN_LON <= lon <= MAX_LON):
                            filtered_ring.append(coord)
                            total_points_filtered += 1
                    
                    # Garder seulement les anneaux avec assez de points
                    if len(filtered_ring) >= 3:  # Minimum pour un polygone
                        filtered_rings.append(filtered_ring)
                
                # Garder seulement les polygones avec des anneaux valides
                if filtered_rings:
                    filtered_geometries.append({
                        'type': 'Polygon',
                        'coordinates': filtered_rings
                    })
        
        # Créer la nouvelle structure
        filtered_data = {
            'type': 'GeometryCollection',
            'geometries': filtered_geometries
        }
        
        # Sauvegarder
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(filtered_data, f, separators=(',', ':'))
        
        print(f"✅ Carte filtrée sauvegardée: {output_file}")
        print(f"📊 Statistiques:")
        print(f"   Points originaux: {total_points_original}")
        print(f"   Points filtrés: {total_points_filtered}")
        print(f"   Géométries: {len(data['geometries'])} → {len(filtered_geometries)}")
        print(f"   Réduction: {((total_points_original - total_points_filtered) / total_points_original * 100):.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_simplified_version(input_file, output_file, simplification_factor=3):
    """
    Crée une version simplifiée en gardant 1 point sur N
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"🔧 Simplification (facteur {simplification_factor})")
        
        simplified_geometries = []
        
        for geometry in data['geometries']:
            if geometry['type'] == 'Polygon':
                simplified_rings = []
                
                for ring in geometry['coordinates']:
                    # Garder 1 point sur N, mais toujours garder le premier et dernier
                    simplified_ring = []
                    for i, coord in enumerate(ring):
                        if i == 0 or i == len(ring) - 1 or i % simplification_factor == 0:
                            simplified_ring.append(coord)
                    
                    if len(simplified_ring) >= 3:
                        simplified_rings.append(simplified_ring)
                
                if simplified_rings:
                    simplified_geometries.append({
                        'type': 'Polygon',
                        'coordinates': simplified_rings
                    })
        
        simplified_data = {
            'type': 'GeometryCollection',
            'geometries': simplified_geometries
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(simplified_data, f, separators=(',', ':'))
        
        print(f"✅ Version simplifiée sauvegardée: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur simplification: {e}")
        return False

if __name__ == "__main__":
    input_file = "morocco-complete-map-geojson-topojson-main/morocco_map.geojson"
    output_file = "morocco_filtered.geojson"
    output_simplified = "morocco_simplified.geojson"
    
    # Créer la version filtrée
    if filter_morocco_map(input_file, output_file):
        print(f"\n🎯 Carte filtrée créée avec succès!")
        
        # Créer aussi une version simplifiée
        create_simplified_version(output_file, output_simplified, 2)
        print(f"🎯 Version simplifiée créée aussi!")
    else:
        print("❌ Échec de la création de la carte filtrée")
