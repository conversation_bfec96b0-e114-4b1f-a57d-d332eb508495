#!/usr/bin/env python3
"""
Script pour analyser les coordonnées du fichier GeoJSON du Maroc
et identifier les zones à enlever (Sahara occidental)
"""

import json
import sys

def analyze_geojson(filename):
    """Analyse les coordonnées du fichier GeoJSON"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📊 Analyse du fichier: {filename}")
        print(f"Type: {data.get('type', 'Unknown')}")
        
        # Extraire toutes les coordonnées
        all_coords = []
        
        if data['type'] == 'GeometryCollection':
            for geometry in data['geometries']:
                if geometry['type'] == 'Polygon':
                    for ring in geometry['coordinates']:
                        all_coords.extend(ring)
        
        if not all_coords:
            print("❌ Aucune coordonnée trouvée")
            return
        
        # Calculer les limites
        lons = [coord[0] for coord in all_coords]
        lats = [coord[1] for coord in all_coords]
        
        min_lon, max_lon = min(lons), max(lons)
        min_lat, max_lat = min(lats), max(lats)
        
        print(f"\n🌍 Limites géographiques:")
        print(f"Longitude: {min_lon:.6f} à {max_lon:.6f}")
        print(f"Latitude:  {min_lat:.6f} à {max_lat:.6f}")
        
        print(f"\n📍 Points extrêmes:")
        print(f"Ouest:  {min_lon:.6f}°")
        print(f"Est:    {max_lon:.6f}°")
        print(f"Sud:    {min_lat:.6f}°")
        print(f"Nord:   {max_lat:.6f}°")
        
        # Analyser la distribution des latitudes pour identifier le Sahara occidental
        print(f"\n🔍 Analyse des latitudes:")
        lat_ranges = {
            "Très Sud (Sahara occidental)": [lat for lat in lats if lat < 24.0],
            "Sud (Frontière Mauritanie)": [lat for lat in lats if 24.0 <= lat < 27.0],
            "Centre (Maroc continental)": [lat for lat in lats if 27.0 <= lat < 32.0],
            "Nord (Côte méditerranéenne)": [lat for lat in lats if lat >= 32.0]
        }
        
        for region, coords in lat_ranges.items():
            if coords:
                print(f"{region}: {len(coords)} points ({min(coords):.2f}° à {max(coords):.2f}°)")
        
        # Suggestions pour le filtrage
        print(f"\n💡 Suggestions pour enlever le Sahara occidental:")
        print(f"- Garder seulement les coordonnées avec latitude > 27.0° (enlève le Sahara)")
        print(f"- Ou garder latitude > 26.0° (garde une petite partie du sud)")
        print(f"- Limiter longitude entre -13.0° et -1.0° (focus sur le Maroc)")
        
        return {
            'min_lon': min_lon, 'max_lon': max_lon,
            'min_lat': min_lat, 'max_lat': max_lat,
            'total_points': len(all_coords)
        }
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

if __name__ == "__main__":
    filename = "morocco-complete-map-geojson-topojson-main/morocco_map.geojson"
    analyze_geojson(filename)
