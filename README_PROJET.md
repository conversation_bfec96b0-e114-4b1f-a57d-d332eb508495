# 🇲🇦 Carte Interactive du Maroc

## 📋 Description

Cette application web interactive affiche une carte du Maroc **sans le Sahara occidental** et offre des outils avancés de dessin, mesure et analyse géographique.

## ✨ Fonctionnalités

### 🎯 Outils de Dessin
- **Cercles** : Dessinez des zones circulaires
- **Rectangles** : Créez des zones rectangulaires  
- **Polygones** : Dessinez des formes personnalisées
- **Marqueurs** : Placez des points d'intérêt

### 📏 Mesure de Distance
- Cliquez sur deux points pour mesurer la distance
- Affichage en kilomètres et miles
- Ligne pointillée pour visualiser la mesure

### 🗂️ Gestion des Données
- **Effacer tout** : Supprime tous les dessins
- **Exporter** : Sauvegarde vos données en JSON
- Compteur d'objets en temps réel

### ℹ️ Informations en Temps Réel
- Niveau de zoom actuel
- Coordonnées du centre de la carte
- Nombre d'objets dessinés

## 🚀 Utilisation

### Démarrage Rapide
1. Ouvrez `morocco_interactive_map.html` dans votre navigateur
2. La carte se charge automatiquement
3. Utilisez les outils dans la barre latérale

### Guide des Outils

#### Dessin
1. Cliquez sur un outil (Cercle, Rectangle, etc.)
2. Le bouton devient vert quand actif
3. Dessinez directement sur la carte
4. Cliquez ailleurs pour terminer

#### Mesure de Distance
1. Cliquez sur "📐 Mesurer"
2. Cliquez sur le premier point
3. Cliquez sur le deuxième point
4. La distance s'affiche automatiquement

#### Export des Données
1. Dessinez vos éléments
2. Cliquez sur "💾 Exporter"
3. Un fichier JSON se télécharge avec :
   - Tous vos dessins
   - Position et zoom de la carte
   - Horodatage

## 📁 Structure du Projet

```
morocco-complete-map-geojson-topojson-main/
├── morocco_interactive_map.html    # Application principale
├── morocco_filtered.geojson        # Carte du Maroc filtrée
├── morocco_simplified.geojson      # Version simplifiée
├── analyze_coordinates.py          # Script d'analyse
├── create_filtered_map.py          # Script de filtrage
└── README_PROJET.md                # Ce fichier
```

## 🔧 Modifications Apportées

### Filtrage Géographique
- **Suppression** du Sahara occidental (latitude < 26.5°)
- **Conservation** du Maroc continental
- **Ajout** d'un peu d'océan à l'ouest
- **Ajout** d'un peu d'Algérie à l'est
- **Ajout** d'un peu de Mauritanie au sud

### Limites Géographiques
- **Latitude** : 26.5° à 36.0°
- **Longitude** : -13.5° à -0.5°
- **Réduction** : 34.9% des points originaux

## 🛠️ Technologies Utilisées

- **Leaflet.js** : Bibliothèque de cartes interactives
- **Leaflet.draw** : Outils de dessin
- **OpenStreetMap** : Tuiles de base
- **GeoJSON** : Format des données géographiques
- **HTML5/CSS3/JavaScript** : Interface utilisateur

## 🎨 Personnalisation

### Couleurs
- **Rouge Maroc** : #c41e3a (boutons, bordures)
- **Vert** : #2e8b57 (outils actifs)
- **Beige** : #ffebcd (remplissage carte)

### Style de la Carte
```javascript
const moroccoStyle = {
    fillColor: '#ffebcd',    // Beige clair
    weight: 2,               // Épaisseur bordure
    opacity: 1,              // Opacité bordure
    color: '#c41e3a',        // Rouge Maroc
    fillOpacity: 0.3         // Transparence remplissage
};
```

## 🔍 Dépannage

### La carte ne se charge pas
- Vérifiez que `morocco_filtered.geojson` est dans le même dossier
- Ouvrez la console développeur (F12) pour voir les erreurs
- Vérifiez votre connexion internet (pour les tuiles)

### Les outils ne fonctionnent pas
- Assurez-vous que JavaScript est activé
- Vérifiez que les CDN Leaflet sont accessibles
- Rechargez la page (Ctrl+F5)

### Performance lente
- Utilisez `morocco_simplified.geojson` pour de meilleures performances
- Réduisez le niveau de zoom pour moins de détails

## 📊 Statistiques

- **Points originaux** : 1,036
- **Points filtrés** : 674
- **Réduction** : 34.9%
- **Taille fichier** : ~50KB (filtré)

## 🚀 Améliorations Possibles

### Fonctionnalités Avancées
- [ ] Calcul de surfaces (polygones)
- [ ] Import de données GPX/KML
- [ ] Couches thématiques (population, relief)
- [ ] Mode plein écran
- [ ] Impression de cartes

### Interface
- [ ] Mode sombre
- [ ] Responsive mobile
- [ ] Raccourcis clavier
- [ ] Aide contextuelle

### Données
- [ ] Données hors ligne
- [ ] Mise à jour automatique
- [ ] Validation des coordonnées
- [ ] Géocodage d'adresses

## 📞 Support

Pour toute question ou amélioration :
1. Vérifiez ce README
2. Consultez la documentation Leaflet
3. Testez avec les fichiers d'exemple

## 📄 Licence

Ce projet utilise des données géographiques libres et des bibliothèques open source.

---

**🎯 Objectif atteint** : Carte du Maroc sans Sahara occidental avec outils interactifs complets !
